# Direct Encoding Fix Script
Write-Host "=== Direct Encoding Fix ===" -ForegroundColor Green

# Key files to fix first
$keyFiles = @(
    "梅花易数-宋-邵雍.txt",
    "周易本义-宋-朱熹.txt",
    "周易正义-唐-孔颖达.txt",
    "伊川易传-宋-程颐.txt"
)

$sourceDir = "知识库"
$targetDir = "知识库_修复后"

foreach ($file in $keyFiles) {
    $sourcePath = Join-Path $sourceDir $file
    $targetPath = Join-Path $targetDir $file
    
    if (Test-Path $sourcePath) {
        Write-Host "Processing: $file" -ForegroundColor Cyan
        
        try {
            # Try GB2312 encoding
            $bytes = [System.IO.File]::ReadAllBytes($sourcePath)
            $gb2312 = [System.Text.Encoding]::GetEncoding("GB2312")
            $content = $gb2312.GetString($bytes)
            
            if ($content -match '[\u4e00-\u9fff]') {
                [System.IO.File]::WriteAllText($targetPath, $content, [System.Text.Encoding]::UTF8)
                Write-Host "  SUCCESS: Fixed with GB2312" -ForegroundColor Green
                continue
            }
        }
        catch {
            Write-Host "  GB2312 failed, trying GBK..." -ForegroundColor Yellow
        }
        
        try {
            # Try GBK encoding
            $gbk = [System.Text.Encoding]::GetEncoding("GBK")
            $content = $gbk.GetString($bytes)
            
            if ($content -match '[\u4e00-\u9fff]') {
                [System.IO.File]::WriteAllText($targetPath, $content, [System.Text.Encoding]::UTF8)
                Write-Host "  SUCCESS: Fixed with GBK" -ForegroundColor Green
                continue
            }
        }
        catch {
            Write-Host "  GBK failed, trying Big5..." -ForegroundColor Yellow
        }
        
        try {
            # Try Big5 encoding
            $big5 = [System.Text.Encoding]::GetEncoding("Big5")
            $content = $big5.GetString($bytes)
            
            if ($content -match '[\u4e00-\u9fff]') {
                [System.IO.File]::WriteAllText($targetPath, $content, [System.Text.Encoding]::UTF8)
                Write-Host "  SUCCESS: Fixed with Big5" -ForegroundColor Green
                continue
            }
        }
        catch {
            Write-Host "  All encodings failed for $file" -ForegroundColor Red
        }
    }
}

Write-Host "Direct encoding fix completed!" -ForegroundColor Green
