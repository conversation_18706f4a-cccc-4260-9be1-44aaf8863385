# Test encoding fix script
Write-Host "Starting test..." -ForegroundColor Green

# Create test directory
$testDir = "Test_Fixed"
if (Test-Path $testDir) {
    Remove-Item $testDir -Recurse -Force
}
New-Item -ItemType Directory -Path $testDir | Out-Null
Write-Host "Created test directory: $testDir" -ForegroundColor Green

# Test one file
$testFile = "梅花易数-宋-邵雍.txt"
$sourcePath = Join-Path "知识库" $testFile
$targetPath = Join-Path $testDir $testFile

if (Test-Path $sourcePath) {
    Write-Host "Testing file: $testFile" -ForegroundColor Cyan
    
    $encodings = @('GB2312', 'GBK', 'Big5', 'UTF-8', 'ASCII', 'Default')
    $success = $false
    
    foreach ($encoding in $encodings) {
        try {
            $content = Get-Content $sourcePath -Encoding $encoding -Raw -ErrorAction Stop
            
            if ($content -and $content.Length -gt 100 -and $content -match '[\u4e00-\u9fff]' -and $content -notmatch '�{3,}') {
                $content | Out-File $targetPath -Encoding UTF8 -NoNewline
                Write-Host "SUCCESS: Fixed using $encoding" -ForegroundColor Green
                $success = $true
                break
            }
        }
        catch {
            Write-Host "Failed with $encoding" -ForegroundColor Red
        }
    }
    
    if (!$success) {
        Write-Host "Could not fix file" -ForegroundColor Red
    }
} else {
    Write-Host "File not found" -ForegroundColor Red
}

Write-Host "Test completed" -ForegroundColor Green
