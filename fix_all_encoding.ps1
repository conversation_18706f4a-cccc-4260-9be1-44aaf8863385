# Fix All Encoding Issues Script
Write-Host "=== Fixing All 54 Garbled Files ===" -ForegroundColor Green

# List of 54 garbled files
$garbledFiles = @(
    "13.第十三册.txt",
    "东坡易传-宋-苏东坡.txt",
    "京氏易传-汉-京房.txt",
    "伊川易传-宋-程颐.txt",
    "合并十八飞星紫微斗数全6卷（古本）.txt",
    "周易--佚名.txt",
    "周易举正-唐-郭京.txt",
    "周易口义-宋-胡瑗.txt",
    "周易合集.txt",
    "周易尚氏学-清-尚秉和.txt",
    "周易本义-宋-朱熹.txt",
    "周易杭氏学-清-杭辛斋.txt",
    "周易正义-唐-孔颖达.txt",
    "周易浅述-清-陈梦雷.txt",
    "周易略例-晋-王弼.txt",
    "周易禅解-明-释智旭.txt",
    "周易述-清-惠栋.txt",
    "周易郑康成注-宋-王应麟.txt",
    "周易阐真-清-刘一明.txt",
    "周易集注-明-来知德.txt",
    "周易集解-唐-李鼎祚.txt",
    "大易象数钩深图-元-张理.txt",
    "天翼-事晴事雨（紫微斗数系列）.txt",
    "子夏易传-春秋-卜子夏.txt",
    "归藏-清-马国翰.txt",
    "御纂周易折中-清-李光地.txt",
    "慧耕-紫微斗数开运全集第3集.txt",
    "新本郑氏周易-清-恵栋.txt",
    "易原-清-多隆阿.txt",
    "易图明辨-清-胡渭.txt",
    "易图通变-元-雷思齐.txt",
    "易学滥觞-元-黄泽.txt",
    "易数钩隐图-宋-刘牧.txt",
    "易童子问-宋-欧阳修.txt",
    "易筮通变-元-雷思齐.txt",
    "易纂言外翼洛书说-元-吴澄.txt",
    "易纬乾元序制记-汉-郑玄.txt",
    "易纬坤灵图-汉-郑玄.txt",
    "易纬是类谋-汉-郑玄.txt",
    "易纬略义-清-张惠言.txt",
    "易纬稽览图-汉-郑玄.txt",
    "易纬辨终备-汉-郑玄.txt",
    "易纬通卦验-汉-郑玄.txt",
    "易经证释-清-陆宗舆.txt",
    "梁若瑜-飞星紫微斗数道藏飞秘的罗辑与功法.txt",
    "梅花易数-宋-邵雍.txt",
    "横渠易说-宋-张载.txt",
    "潘子渔-紫微斗数精奥.txt",
    "紫云：斗数论子女.txt",
    "紫微斗数绝学第1集.txt",
    "紫微斗数论命要诀.txt",
    "连山易-清-马国翰.txt",
    "通志堂经解.013-014.清.纳兰成德编.周易裨传一卷.外篇一卷.宋.林至撰，易图说三卷.宋.吴仁杰撰.清康熙时期刊本.txt",
    "陆氏易解-明-姚士粦.txt",
    "顾祥弘-飞星紫微斗数命身十二宫详解.txt"
)

$successCount = 0
$failedFiles = @()

Write-Host "Processing $($garbledFiles.Count) files..." -ForegroundColor Yellow

foreach ($file in $garbledFiles) {
    Write-Host "Processing: $file" -ForegroundColor Cyan
    
    try {
        # Read source file as bytes
        $sourcePath = Join-Path "知识库" $file
        $targetPath = Join-Path "知识库_修复后" $file
        
        if (Test-Path $sourcePath) {
            $bytes = [System.IO.File]::ReadAllBytes($sourcePath)
            
            # Try GB2312 first (most common for Chinese texts)
            try {
                $gb2312 = [System.Text.Encoding]::GetEncoding("GB2312")
                $content = $gb2312.GetString($bytes)
                
                if ($content -and $content.Length -gt 50 -and $content -match '[\u4e00-\u9fff]') {
                    [System.IO.File]::WriteAllText($targetPath, $content, [System.Text.Encoding]::UTF8)
                    Write-Host "  SUCCESS: Fixed with GB2312" -ForegroundColor Green
                    $successCount++
                    continue
                }
            }
            catch {
                # Continue to next encoding
            }
            
            # Try GBK
            try {
                $gbk = [System.Text.Encoding]::GetEncoding("GBK")
                $content = $gbk.GetString($bytes)
                
                if ($content -and $content.Length -gt 50 -and $content -match '[\u4e00-\u9fff]') {
                    [System.IO.File]::WriteAllText($targetPath, $content, [System.Text.Encoding]::UTF8)
                    Write-Host "  SUCCESS: Fixed with GBK" -ForegroundColor Green
                    $successCount++
                    continue
                }
            }
            catch {
                # Continue to next encoding
            }
            
            # Try Big5
            try {
                $big5 = [System.Text.Encoding]::GetEncoding("Big5")
                $content = $big5.GetString($bytes)
                
                if ($content -and $content.Length -gt 50 -and $content -match '[\u4e00-\u9fff]') {
                    [System.IO.File]::WriteAllText($targetPath, $content, [System.Text.Encoding]::UTF8)
                    Write-Host "  SUCCESS: Fixed with Big5" -ForegroundColor Green
                    $successCount++
                    continue
                }
            }
            catch {
                # Continue to next encoding
            }
            
            # If all fail, mark as failed
            Write-Host "  FAILED: Could not fix encoding" -ForegroundColor Red
            $failedFiles += $file
        }
        else {
            Write-Host "  ERROR: Source file not found" -ForegroundColor Red
            $failedFiles += $file
        }
    }
    catch {
        Write-Host "  ERROR: Exception occurred - $($_.Exception.Message)" -ForegroundColor Red
        $failedFiles += $file
    }
}

Write-Host "`n=== Final Results ===" -ForegroundColor Green
Write-Host "Total files processed: $($garbledFiles.Count)" -ForegroundColor Cyan
Write-Host "Successfully fixed: $successCount" -ForegroundColor Green
Write-Host "Failed to fix: $($failedFiles.Count)" -ForegroundColor Red

if ($failedFiles.Count -gt 0) {
    Write-Host "`nFailed files:" -ForegroundColor Yellow
    foreach ($file in $failedFiles) {
        Write-Host "  - $file" -ForegroundColor Red
    }
}

# Final verification
$totalFiles = (Get-ChildItem "知识库_修复后" -Filter "*.txt").Count
Write-Host "`nTotal files in target directory: $totalFiles" -ForegroundColor Cyan
Write-Host "Encoding fix process completed!" -ForegroundColor Green
