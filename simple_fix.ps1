# 简化的编码修复脚本
Write-Host "开始处理知识库编码问题..." -ForegroundColor Green

# 确保修复目录存在
if (!(Test-Path "知识库_修复后")) {
    New-Item -ItemType Directory -Path "知识库_修复后"
}

# 获取所有txt文件
$allFiles = Get-ChildItem "知识库" -Filter "*.txt"
$processedCount = 0
$successCount = 0

foreach ($file in $allFiles) {
    $sourcePath = $file.FullName
    $targetPath = Join-Path "知识库_修复后" $file.Name
    $processedCount++
    
    Write-Host "处理文件 $processedCount/$($allFiles.Count): $($file.Name)" -ForegroundColor Cyan
    
    # 尝试不同编码
    $encodings = @('UTF8', 'GB2312', 'GBK', 'Big5', 'ASCII', 'Default')
    $success = $false
    
    foreach ($encoding in $encodings) {
        try {
            $content = Get-Content $sourcePath -Encoding $encoding -Raw -ErrorAction Stop
            
            # 检查内容是否包含正常中文且没有乱码
            if ($content -and $content.Length -gt 10 -and 
                $content -match '[\u4e00-\u9fff]' -and 
                $content -notmatch '�{3,}' -and
                $content -notmatch '[^\u0000-\u007F\u4e00-\u9fff\u3400-\u4dbf\uf900-\ufaff\u3000-\u303f\uff00-\uffef]{10,}') {
                
                # 保存为UTF8
                $content | Out-File $targetPath -Encoding UTF8 -NoNewline
                Write-Host "  ✓ 使用 $encoding 编码成功修复" -ForegroundColor Green
                $success = $true
                $successCount++
                break
            }
        } catch {
            continue
        }
    }
    
    if (!$success) {
        # 如果都失败了，直接复制原文件
        Copy-Item $sourcePath $targetPath
        Write-Host "  ⚠ 保持原样复制" -ForegroundColor Yellow
    }
}

Write-Host "`n处理完成！" -ForegroundColor Green
Write-Host "总文件数: $($allFiles.Count)" -ForegroundColor Cyan
Write-Host "成功修复: $successCount" -ForegroundColor Green
Write-Host "保持原样: $($allFiles.Count - $successCount)" -ForegroundColor Yellow
