# 知识库文件编码修复脚本
# 基于检测结果，处理编码有问题的文件

# 编码有问题的文件列表
$garbledFiles = @(
    "13.第十三册.txt",
    "东坡易传-宋-苏东坡.txt",
    "京氏易传-汉-京房.txt", 
    "伊川易传-宋-程颐.txt",
    "合并十八飞星紫微斗数全6卷（古本）.txt",
    "周易--佚名.txt",
    "周易举正-唐-郭京.txt",
    "周易口义-宋-胡瑗.txt",
    "周易合集.txt",
    "周易尚氏学-清-尚秉和.txt",
    "周易本义-宋-朱熹.txt",
    "周易杭氏学-清-杭辛斋.txt",
    "周易正义-唐-孔颖达.txt",
    "周易浅述-清-陈梦雷.txt",
    "周易略例-晋-王弼.txt",
    "周易禅解-明-释智旭.txt",
    "周易述-清-惠栋.txt",
    "周易郑康成注-宋-王应麟.txt",
    "周易阐真-清-刘一明.txt",
    "周易集注-明-来知德.txt",
    "周易集解-唐-李鼎祚.txt",
    "大易象数钩深图-元-张理.txt",
    "天翼-事晴事雨（紫微斗数系列）.txt",
    "子夏易传-春秋-卜子夏.txt",
    "归藏-清-马国翰.txt",
    "御纂周易折中-清-李光地.txt",
    "慧耕-紫微斗数开运全集第3集.txt",
    "新本郑氏周易-清-恵栋.txt",
    "易原-清-多隆阿.txt",
    "易图明辨-清-胡渭.txt",
    "易图通变-元-雷思齐.txt",
    "易学滥觞-元-黄泽.txt",
    "易数钩隐图-宋-刘牧.txt",
    "易童子问-宋-欧阳修.txt",
    "易筮通变-元-雷思齐.txt",
    "易纂言外翼洛书说-元-吴澄.txt",
    "易纬乾元序制记-汉-郑玄.txt",
    "易纬坤灵图-汉-郑玄.txt",
    "易纬是类谋-汉-郑玄.txt",
    "易纬略义-清-张惠言.txt",
    "易纬稽览图-汉-郑玄.txt",
    "易纬辨终备-汉-郑玄.txt",
    "易纬通卦验-汉-郑玄.txt",
    "易经证释-清-陆宗舆.txt",
    "梁若瑜-飞星紫微斗数道藏飞秘的罗辑与功法.txt",
    "梅花易数-宋-邵雍.txt",
    "横渠易说-宋-张载.txt",
    "潘子渔-紫微斗数精奥.txt",
    "紫云：斗数论子女.txt",
    "紫微斗数绝学第1集.txt",
    "紫微斗数论命要诀.txt",
    "连山易-清-马国翰.txt",
    "通志堂经解.013-014.清.纳兰成德编.周易裨传一卷.外篇一卷.宋.林至撰，易图说三卷.宋.吴仁杰撰.清康熙时期刊本.txt",
    "陆氏易解-明-姚士粦.txt",
    "顾祥弘-飞星紫微斗数命身十二宫详解.txt"
)

# 错误文件（需要特殊处理）
$errorFiles = @(
    "四库全书.子部.术数类.28.京氏易传-[汉]京房撰[吴]陆绩注(1).txt",
    "四库全书.子部.术数类.28.京氏易传-[汉]京房撰[吴]陆绩注.txt",
    "道藏紫微斗数[共3卷] .txt"
)

Write-Host "=== 知识库编码问题统计 ===" -ForegroundColor Green
Write-Host "编码有问题的文件数量: $($garbledFiles.Count)" -ForegroundColor Yellow
Write-Host "需要特殊处理的文件数量: $($errorFiles.Count)" -ForegroundColor Red

# 创建备份目录
$backupDir = "知识库_备份_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
if (!(Test-Path $backupDir)) {
    New-Item -ItemType Directory -Path $backupDir
    Write-Host "创建备份目录: $backupDir" -ForegroundColor Green
}

# 创建修复后的目录
$fixedDir = "知识库_修复后"
if (!(Test-Path $fixedDir)) {
    New-Item -ItemType Directory -Path $fixedDir
    Write-Host "创建修复目录: $fixedDir" -ForegroundColor Green
}

Write-Host "`n=== 开始处理编码问题 ===" -ForegroundColor Green

# 处理编码有问题的文件
foreach ($file in $garbledFiles) {
    $sourcePath = Join-Path "知识库" $file
    $backupPath = Join-Path $backupDir $file
    $fixedPath = Join-Path $fixedDir $file
    
    if (Test-Path $sourcePath) {
        Write-Host "处理文件: $file" -ForegroundColor Cyan
        
        # 备份原文件
        Copy-Item $sourcePath $backupPath
        
        # 尝试不同编码读取和转换
        $encodings = @('GB2312', 'GBK', 'Big5', 'UTF-8', 'ASCII', 'Default')
        $success = $false
        
        foreach ($encoding in $encodings) {
            try {
                $content = Get-Content $sourcePath -Encoding $encoding -Raw
                # 检查是否包含正常中文字符
                if ($content -match '[\u4e00-\u9fff]' -and $content -notmatch '�{2,}') {
                    # 转换为UTF-8保存
                    $content | Out-File $fixedPath -Encoding UTF8
                    Write-Host "  ✓ 成功使用 $encoding 编码修复" -ForegroundColor Green
                    $success = $true
                    break
                }
            } catch {
                continue
            }
        }
        
        if (!$success) {
            Write-Host "  ✗ 无法修复，保持原样" -ForegroundColor Red
            Copy-Item $sourcePath $fixedPath
        }
    } else {
        Write-Host "文件不存在: $file" -ForegroundColor Red
    }
}

Write-Host "`n=== 复制正常文件 ===" -ForegroundColor Green

# 复制所有正常的文件到修复目录
Get-ChildItem "知识库" -Filter "*.txt" | ForEach-Object {
    if ($_.Name -notin $garbledFiles -and $_.Name -notin $errorFiles) {
        $sourcePath = $_.FullName
        $fixedPath = Join-Path $fixedDir $_.Name
        Copy-Item $sourcePath $fixedPath
        Write-Host "复制正常文件: $($_.Name)" -ForegroundColor Gray
    }
}

Write-Host "`n=== 处理完成 ===" -ForegroundColor Green
Write-Host "原文件备份在: $backupDir" -ForegroundColor Yellow
Write-Host "修复后文件在: $fixedDir" -ForegroundColor Yellow

# 统计结果
$totalFiles = (Get-ChildItem "知识库" -Filter "*.txt").Count
$fixedFiles = (Get-ChildItem $fixedDir -Filter "*.txt").Count
Write-Host "`n原始文件总数: $totalFiles" -ForegroundColor Cyan
Write-Host "修复后文件数: $fixedFiles" -ForegroundColor Cyan

if ($fixedFiles -eq $totalFiles - $errorFiles.Count) {
    Write-Host "✓ 所有可修复文件处理完成！" -ForegroundColor Green
} else {
    Write-Host "⚠ 部分文件可能需要手动处理" -ForegroundColor Yellow
}
