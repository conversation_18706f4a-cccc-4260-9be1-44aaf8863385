# Complete Knowledge Base Encoding Fix Script
# Fix all encoding issues in the knowledge base files

# List of files with encoding problems
$garbledFiles = @(
    "13.第十三册.txt",
    "东坡易传-宋-苏东坡.txt",
    "京氏易传-汉-京房.txt",
    "伊川易传-宋-程颐.txt",
    "合并十八飞星紫微斗数全6卷（古本）.txt",
    "周易--佚名.txt",
    "周易举正-唐-郭京.txt",
    "周易口义-宋-胡瑗.txt",
    "周易合集.txt",
    "周易尚氏学-清-尚秉和.txt",
    "周易本义-宋-朱熹.txt",
    "周易杭氏学-清-杭辛斋.txt",
    "周易正义-唐-孔颖达.txt",
    "周易浅述-清-陈梦雷.txt",
    "周易略例-晋-王弼.txt",
    "周易禅解-明-释智旭.txt",
    "周易述-清-惠栋.txt",
    "周易郑康成注-宋-王应麟.txt",
    "周易阐真-清-刘一明.txt",
    "周易集注-明-来知德.txt",
    "周易集解-唐-李鼎祚.txt",
    "大易象数钩深图-元-张理.txt",
    "天翼-事晴事雨（紫微斗数系列）.txt",
    "子夏易传-春秋-卜子夏.txt",
    "归藏-清-马国翰.txt",
    "御纂周易折中-清-李光地.txt",
    "慧耕-紫微斗数开运全集第3集.txt",
    "新本郑氏周易-清-恵栋.txt",
    "易原-清-多隆阿.txt",
    "易图明辨-清-胡渭.txt",
    "易图通变-元-雷思齐.txt",
    "易学滥觞-元-黄泽.txt",
    "易数钩隐图-宋-刘牧.txt",
    "易童子问-宋-欧阳修.txt",
    "易筮通变-元-雷思齐.txt",
    "易纂言外翼洛书说-元-吴澄.txt",
    "易纬乾元序制记-汉-郑玄.txt",
    "易纬坤灵图-汉-郑玄.txt",
    "易纬是类谋-汉-郑玄.txt",
    "易纬略义-清-张惠言.txt",
    "易纬稽览图-汉-郑玄.txt",
    "易纬辨终备-汉-郑玄.txt",
    "易纬通卦验-汉-郑玄.txt",
    "易经证释-清-陆宗舆.txt",
    "梁若瑜-飞星紫微斗数道藏飞秘的罗辑与功法.txt",
    "梅花易数-宋-邵雍.txt",
    "横渠易说-宋-张载.txt",
    "潘子渔-紫微斗数精奥.txt",
    "紫云：斗数论子女.txt",
    "紫微斗数绝学第1集.txt",
    "紫微斗数论命要诀.txt",
    "连山易-清-马国翰.txt",
    "通志堂经解.013-014.清.纳兰成德编.周易裨传一卷.外篇一卷.宋.林至撰，易图说三卷.宋.吴仁杰撰.清康熙时期刊本.txt",
    "陆氏易解-明-姚士粦.txt",
    "顾祥弘-飞星紫微斗数命身十二宫详解.txt"
)

# Error files that need special handling
$errorFiles = @(
    "四库全书.子部.术数类.28.京氏易传-[汉]京房撰[吴]陆绩注(1).txt",
    "四库全书.子部.术数类.28.京氏易传-[汉]京房撰[吴]陆绩注.txt",
    "道藏紫微斗数[共3卷] .txt"
)

Write-Host "=== Knowledge Base Encoding Fix Statistics ===" -ForegroundColor Green
Write-Host "Files with encoding problems: $($garbledFiles.Count)" -ForegroundColor Yellow
Write-Host "Files needing special handling: $($errorFiles.Count)" -ForegroundColor Red

# Create backup directory
$timestamp = Get-Date -Format 'yyyyMMdd_HHmmss'
$backupDir = "KnowledgeBase_Backup_$timestamp"
if (!(Test-Path $backupDir)) {
    New-Item -ItemType Directory -Path $backupDir | Out-Null
    Write-Host "Created backup directory: $backupDir" -ForegroundColor Green
}

# Create fixed directory
$fixedDir = "KnowledgeBase_Fixed"
if (Test-Path $fixedDir) {
    Remove-Item $fixedDir -Recurse -Force
}
New-Item -ItemType Directory -Path $fixedDir | Out-Null
Write-Host "Created fixed directory: $fixedDir" -ForegroundColor Green

Write-Host "`n=== Starting encoding fix process ===" -ForegroundColor Green

# Function to test encoding and validate content
function Test-EncodingAndContent {
    param(
        [string]$FilePath,
        [string]$Encoding
    )

    try {
        $content = Get-Content $FilePath -Encoding $Encoding -Raw -ErrorAction Stop

        # Check if content exists and has reasonable length
        if (!$content -or $content.Length -lt 10) {
            return $false
        }

        # Check for Chinese characters and no garbled text
        $hasChinese = $content -match '[\u4e00-\u9fff]'
        $hasGarbled = $content -match '�{2,}' -or $content -match '[^\u0000-\u007F\u4e00-\u9fff\u3400-\u4dbf\uf900-\ufaff\u3000-\u303f\uff00-\uffef]{10,}'

        return $hasChinese -and !$hasGarbled
    }
    catch {
        return $false
    }
}

# Process files with encoding problems
$processedCount = 0
$successCount = 0

foreach ($file in $garbledFiles) {
    $sourcePath = Join-Path "知识库" $file
    $backupPath = Join-Path $backupDir $file
    $fixedPath = Join-Path $fixedDir $file
    $processedCount++

    if (Test-Path $sourcePath) {
        Write-Host "Processing file $processedCount/$($garbledFiles.Count): $file" -ForegroundColor Cyan

        # Backup original file
        Copy-Item $sourcePath $backupPath

        # Try different encodings
        $encodings = @('GB2312', 'GBK', 'Big5', 'UTF-8', 'ASCII', 'Default', 'Unicode', 'UTF32')
        $success = $false

        foreach ($encoding in $encodings) {
            if (Test-EncodingAndContent -FilePath $sourcePath -Encoding $encoding) {
                try {
                    $content = Get-Content $sourcePath -Encoding $encoding -Raw
                    $content | Out-File $fixedPath -Encoding UTF8 -NoNewline
                    Write-Host "  SUCCESS: Fixed using $encoding encoding" -ForegroundColor Green
                    $success = $true
                    $successCount++
                    break
                }
                catch {
                    continue
                }
            }
        }

        if (!$success) {
            Write-Host "  FAILED: Could not fix, keeping original" -ForegroundColor Red
            Copy-Item $sourcePath $fixedPath
        }
    } else {
        Write-Host "File not found: $file" -ForegroundColor Red
    }
}

Write-Host "`n=== Copying normal files ===" -ForegroundColor Green

# Copy all normal files to fixed directory
$normalFileCount = 0
Get-ChildItem "知识库" -Filter "*.txt" | ForEach-Object {
    if ($_.Name -notin $garbledFiles -and $_.Name -notin $errorFiles) {
        $sourcePath = $_.FullName
        $fixedPath = Join-Path $fixedDir $_.Name
        Copy-Item $sourcePath $fixedPath
        $normalFileCount++
        if ($normalFileCount % 50 -eq 0) {
            Write-Host "Copied $normalFileCount normal files..." -ForegroundColor Gray
        }
    }
}

Write-Host "`n=== Process completed ===" -ForegroundColor Green
Write-Host "Original files backed up to: $backupDir" -ForegroundColor Yellow
Write-Host "Fixed files saved to: $fixedDir" -ForegroundColor Yellow

# Statistics
$totalFiles = (Get-ChildItem "知识库" -Filter "*.txt").Count
$fixedFiles = (Get-ChildItem $fixedDir -Filter "*.txt").Count
Write-Host "`nOriginal file count: $totalFiles" -ForegroundColor Cyan
Write-Host "Fixed file count: $fixedFiles" -ForegroundColor Cyan
Write-Host "Successfully fixed garbled files: $successCount/$($garbledFiles.Count)" -ForegroundColor Cyan
Write-Host "Normal files copied: $normalFileCount" -ForegroundColor Cyan

if ($fixedFiles -eq $totalFiles - $errorFiles.Count) {
    Write-Host "SUCCESS: All fixable files have been processed!" -ForegroundColor Green
} else {
    Write-Host "WARNING: Some files may need manual processing" -ForegroundColor Yellow
}

# Create summary report
$reportPath = Join-Path $fixedDir "encoding_fix_report.txt"
$report = @"
Encoding Fix Report
Generated: $(Get-Date)

Total files processed: $totalFiles
Files with encoding problems: $($garbledFiles.Count)
Successfully fixed: $successCount
Normal files copied: $normalFileCount
Files needing special handling: $($errorFiles.Count)

Fixed files location: $fixedDir
Backup location: $backupDir
"@

$report | Out-File $reportPath -Encoding UTF8
Write-Host "`nReport saved to: $reportPath" -ForegroundColor Green
