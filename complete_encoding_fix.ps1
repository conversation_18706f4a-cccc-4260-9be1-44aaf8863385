# Complete Encoding Fix Script for 54 Garbled Files
Write-Host "=== Complete Encoding Fix for 54 Garbled Files ===" -ForegroundColor Green

# List of 54 garbled files that need encoding fix
$garbledFiles = @(
    "13.第十三册.txt",
    "东坡易传-宋-苏东坡.txt",
    "京氏易传-汉-京房.txt",
    "伊川易传-宋-程颐.txt",
    "合并十八飞星紫微斗数全6卷（古本）.txt",
    "周易--佚名.txt",
    "周易举正-唐-郭京.txt",
    "周易口义-宋-胡瑗.txt",
    "周易合集.txt",
    "周易尚氏学-清-尚秉和.txt",
    "周易本义-宋-朱熹.txt",
    "周易杭氏学-清-杭辛斋.txt",
    "周易正义-唐-孔颖达.txt",
    "周易浅述-清-陈梦雷.txt",
    "周易略例-晋-王弼.txt",
    "周易禅解-明-释智旭.txt",
    "周易述-清-惠栋.txt",
    "周易郑康成注-宋-王应麟.txt",
    "周易阐真-清-刘一明.txt",
    "周易集注-明-来知德.txt",
    "周易集解-唐-李鼎祚.txt",
    "大易象数钩深图-元-张理.txt",
    "天翼-事晴事雨（紫微斗数系列）.txt",
    "子夏易传-春秋-卜子夏.txt",
    "归藏-清-马国翰.txt",
    "御纂周易折中-清-李光地.txt",
    "慧耕-紫微斗数开运全集第3集.txt",
    "新本郑氏周易-清-恵栋.txt",
    "易原-清-多隆阿.txt",
    "易图明辨-清-胡渭.txt",
    "易图通变-元-雷思齐.txt",
    "易学滥觞-元-黄泽.txt",
    "易数钩隐图-宋-刘牧.txt",
    "易童子问-宋-欧阳修.txt",
    "易筮通变-元-雷思齐.txt",
    "易纂言外翼洛书说-元-吴澄.txt",
    "易纬乾元序制记-汉-郑玄.txt",
    "易纬坤灵图-汉-郑玄.txt",
    "易纬是类谋-汉-郑玄.txt",
    "易纬略义-清-张惠言.txt",
    "易纬稽览图-汉-郑玄.txt",
    "易纬辨终备-汉-郑玄.txt",
    "易纬通卦验-汉-郑玄.txt",
    "易经证释-清-陆宗舆.txt",
    "梁若瑜-飞星紫微斗数道藏飞秘的罗辑与功法.txt",
    "梅花易数-宋-邵雍.txt",
    "横渠易说-宋-张载.txt",
    "潘子渔-紫微斗数精奥.txt",
    "紫云：斗数论子女.txt",
    "紫微斗数绝学第1集.txt",
    "紫微斗数论命要诀.txt",
    "连山易-清-马国翰.txt",
    "通志堂经解.013-014.清.纳兰成德编.周易裨传一卷.外篇一卷.宋.林至撰，易图说三卷.宋.吴仁杰撰.清康熙时期刊本.txt",
    "陆氏易解-明-姚士粦.txt",
    "顾祥弘-飞星紫微斗数命身十二宫详解.txt"
)

$sourceDir = "知识库"
$targetDir = "知识库_修复后"
$successCount = 0
$failedFiles = @()

Write-Host "Starting to fix $($garbledFiles.Count) garbled files..." -ForegroundColor Yellow

# Function to try multiple encoding methods
function Fix-FileEncoding {
    param(
        [string]$SourcePath,
        [string]$TargetPath,
        [string]$FileName
    )
    
    # Method 1: Try PowerShell built-in encodings
    $psEncodings = @('Default', 'ASCII', 'UTF8', 'Unicode', 'BigEndianUnicode', 'UTF32', 'BigEndianUTF32')
    
    foreach ($encoding in $psEncodings) {
        try {
            $content = Get-Content $SourcePath -Encoding $encoding -Raw -ErrorAction Stop
            
            if ($content -and $content.Length -gt 100) {
                # Check for Chinese characters
                if ($content -match '[\u4e00-\u9fff]') {
                    $content | Out-File $TargetPath -Encoding UTF8 -NoNewline
                    Write-Host "  SUCCESS: Fixed using PowerShell $encoding" -ForegroundColor Green
                    return $true
                }
            }
        }
        catch {
            continue
        }
    }
    
    # Method 2: Try .NET encoding classes
    try {
        $encodings = @(
            [System.Text.Encoding]::GetEncoding("GB2312"),
            [System.Text.Encoding]::GetEncoding("GBK"),
            [System.Text.Encoding]::GetEncoding("Big5"),
            [System.Text.Encoding]::GetEncoding("GB18030")
        )
        
        $bytes = [System.IO.File]::ReadAllBytes($SourcePath)
        
        foreach ($enc in $encodings) {
            try {
                $content = $enc.GetString($bytes)
                
                if ($content -and $content.Length -gt 100 -and $content -match '[\u4e00-\u9fff]') {
                    [System.IO.File]::WriteAllText($TargetPath, $content, [System.Text.Encoding]::UTF8)
                    Write-Host "  SUCCESS: Fixed using .NET $($enc.EncodingName)" -ForegroundColor Green
                    return $true
                }
            }
            catch {
                continue
            }
        }
    }
    catch {
        # Continue to next method
    }
    
    # Method 3: Try reading as bytes and converting
    try {
        $bytes = [System.IO.File]::ReadAllBytes($SourcePath)
        
        # Try different code pages
        $codePages = @(936, 950, 54936, 20936)  # GB2312, Big5, GB18030, etc.
        
        foreach ($cp in $codePages) {
            try {
                $encoding = [System.Text.Encoding]::GetEncoding($cp)
                $content = $encoding.GetString($bytes)
                
                if ($content -and $content.Length -gt 100 -and $content -match '[\u4e00-\u9fff]') {
                    [System.IO.File]::WriteAllText($TargetPath, $content, [System.Text.Encoding]::UTF8)
                    Write-Host "  SUCCESS: Fixed using CodePage $cp" -ForegroundColor Green
                    return $true
                }
            }
            catch {
                continue
            }
        }
    }
    catch {
        # Continue
    }
    
    return $false
}

# Process each garbled file
foreach ($file in $garbledFiles) {
    $sourcePath = Join-Path $sourceDir $file
    $targetPath = Join-Path $targetDir $file
    
    if (Test-Path $sourcePath) {
        Write-Host "Processing: $file" -ForegroundColor Cyan
        
        if (Fix-FileEncoding -SourcePath $sourcePath -TargetPath $targetPath -FileName $file) {
            $successCount++
        } else {
            Write-Host "  FAILED: Could not fix encoding for $file" -ForegroundColor Red
            $failedFiles += $file
            # Keep original file as fallback
            Copy-Item $sourcePath $targetPath -Force
        }
    } else {
        Write-Host "File not found: $file" -ForegroundColor Red
        $failedFiles += $file
    }
}

Write-Host "`n=== Encoding Fix Results ===" -ForegroundColor Green
Write-Host "Total garbled files: $($garbledFiles.Count)" -ForegroundColor Cyan
Write-Host "Successfully fixed: $successCount" -ForegroundColor Green
Write-Host "Failed to fix: $($failedFiles.Count)" -ForegroundColor Red

if ($failedFiles.Count -gt 0) {
    Write-Host "`nFailed files:" -ForegroundColor Yellow
    foreach ($file in $failedFiles) {
        Write-Host "  - $file" -ForegroundColor Red
    }
}

# Verify total file count
$totalFiles = (Get-ChildItem $targetDir -Filter "*.txt").Count
Write-Host "`nTotal files in target directory: $totalFiles" -ForegroundColor Cyan

Write-Host "`nEncoding fix process completed!" -ForegroundColor Green
