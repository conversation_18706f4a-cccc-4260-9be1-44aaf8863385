@echo off
echo === Fixing Encoding Issues ===

powershell -Command "$file = '梅花易数-宋-邵雍.txt'; $sourcePath = '知识库\' + $file; $targetPath = '知识库_修复后\' + $file; $bytes = [System.IO.File]::ReadAllBytes($sourcePath); $gb2312 = [System.Text.Encoding]::GetEncoding('GB2312'); $content = $gb2312.GetString($bytes); [System.IO.File]::WriteAllText($targetPath, $content, [System.Text.Encoding]::UTF8); Write-Host 'Fixed梅花易数' -ForegroundColor Green"

powershell -Command "$file = '周易本义-宋-朱熹.txt'; $sourcePath = '知识库\' + $file; $targetPath = '知识库_修复后\' + $file; $bytes = [System.IO.File]::ReadAllBytes($sourcePath); $gb2312 = [System.Text.Encoding]::GetEncoding('GB2312'); $content = $gb2312.GetString($bytes); [System.IO.File]::WriteAllText($targetPath, $content, [System.Text.Encoding]::UTF8); Write-Host 'Fixed周易本义' -ForegroundColor Green"

powershell -Command "$file = '周易正义-唐-孔颖达.txt'; $sourcePath = '知识库\' + $file; $targetPath = '知识库_修复后\' + $file; $bytes = [System.IO.File]::ReadAllBytes($sourcePath); $gb2312 = [System.Text.Encoding]::GetEncoding('GB2312'); $content = $gb2312.GetString($bytes); [System.IO.File]::WriteAllText($targetPath, $content, [System.Text.Encoding]::UTF8); Write-Host 'Fixed周易正义' -ForegroundColor Green"

powershell -Command "$file = '伊川易传-宋-程颐.txt'; $sourcePath = '知识库\' + $file; $targetPath = '知识库_修复后\' + $file; $bytes = [System.IO.File]::ReadAllBytes($sourcePath); $gb2312 = [System.Text.Encoding]::GetEncoding('GB2312'); $content = $gb2312.GetString($bytes); [System.IO.File]::WriteAllText($targetPath, $content, [System.Text.Encoding]::UTF8); Write-Host 'Fixed伊川易传' -ForegroundColor Green"

echo Key files fixed!
pause
